package integral

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	integralReq "github.com/flipped-aurora/gin-vue-admin/server/model/integral/request"
	integralRes "github.com/flipped-aurora/gin-vue-admin/server/model/integral/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
)

type SysUserPointsService struct{}

var SysUserPointsServiceApp = new(SysUserPointsService)

// GetUserPointsRecords 获取用户积分消耗记录
// @author: [yourname]
// @function: GetUserPointsRecords
// @description: 根据用户ID和类型获取积分消耗记录
// @param: userID uint, info systemReq.SysUserPointsSearch
// @return: response integralRes.UserTaskListResponse, err error
func (s *SysUserPointsService) GetUserPointsRecords(userID uint, info integralReq.SysUserPointsSearch) (response integralRes.UserTaskListResponse, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 创建基础查询
	db := global.GVA_DB.Model(&integral.SysUserPoints{}).Where("sys_user_points.user_id = ?", userID)

	// 如果指定了类型，添加类型过滤
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}

	// 只查询消耗记录（负数）
	db = db.Where("`change` < 0")

	// 获取总数
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 混合查询所有类型的记录，根据type字段动态处理reason
	var records []struct {
		ID     uint            `json:"id"`
		Time   global.DateTime `json:"time"`
		Change int             `json:"change"`
		Type   string          `json:"type"`
		Reason string          `json:"reason"`
		Prompt *string         `json:"prompt"` // 可能为空的prompt字段
	}

	err = db.Select("sys_user_points.id, sys_user_points.created_at as time, sys_user_points.`change`, sys_user_points.type, sys_user_points.reason, llm_usage_logs.prompt").
		Joins("LEFT JOIN llm_usage_logs ON sys_user_points.usage_log_id = llm_usage_logs.id AND sys_user_points.type = 'llm'").
		Order("sys_user_points.created_at DESC").
		Limit(limit).Offset(offset).
		Find(&records).Error

	if err != nil {
		return
	}

	// 转换为响应格式，根据type动态处理reason字段
	// 初始化为空数组，确保List字段不为nil
	taskItems := make([]integralRes.UserTaskItem, 0, len(records))
	for _, record := range records {
		var title string
		if record.Type == "llm" && record.Prompt != nil && *record.Prompt != "" {
			// 对于llm类型，优先使用prompt作为title
			title = *record.Prompt
		} else {
			// 对于其他类型或prompt为空时，使用原始reason
			title = record.Reason
		}

		taskItems = append(taskItems, integralRes.UserTaskItem{
			ID:          record.ID,
			CompletedAt: record.Time,
			Points:      -record.Change, // 转换为正数，因为原来是负数
			Title:       title,
		})
	}

	return integralRes.UserTaskListResponse{
		List:     taskItems,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, nil
}

// mcp调用失败回滚积分
func (s *SysUserPointsService) AddPoints(userID uint, points int, reason string, projectId uint, projectToolId uint) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {

		// 1. 更新用户积分
		if err := tx.Model(&system.SysUser{}).Where("id = ?", userID).
			UpdateColumn("points", gorm.Expr("points + ?", points)).Error; err != nil {
			return fmt.Errorf("更新用户积分失败: %v", err)
		}

		// 2. 记录积分流水
		pointsRecord := integral.SysUserPoints{
			UserID:        userID,
			Change:        points,
			Reason:        reason,
			Type:          "mcp",
			ProjectID:     projectId,
			ProjectToolID: projectToolId,
		}

		if err := tx.Create(&pointsRecord).Error; err != nil {
			return fmt.Errorf("记录积分流水失败: %v", err)
		}

		return nil
	})
}
